import FroalaEditor from 'froala-editor';
import { useEffect } from 'react';
import useT from '../../../utils/Translations/useT';

export interface ICustomButton {
  id: string;
  title: string;
  icon: string;
  className: string;
  onClick: () => void;
}

interface UseCustomButtonsProps {
  customButtons?: ICustomButton[];
}

const useCustomButtons = ({
  customButtons = [],
}: UseCustomButtonsProps = {}) => {
  const t = useT();

  useEffect(() => {
    if (!customButtons.length) return;
    FroalaEditor.DefineIconTemplate(
      'ico_moon',
      '<i class="icon-[NAME] [CLASS_NAME]"></i>',
    );
    customButtons.forEach(button => {
      console.log('button', button);
      FroalaEditor.DefineIcon(button.icon, {
        NAME: button.icon,
        template: 'ico_moon',
        CLASS_NAME: button.className,
      });

      FroalaEditor.RegisterCommand(button.id, {
        title: t(button.title),
        type: 'button',
        icon: button.icon,
        undo: false,
        focus: false,
        refreshAfterCallback: false,
        callback() {
          if (button.onClick) {
            button.onClick();
          }
        },
      });
    });
  }, [customButtons, t]);
};

export default useCustomButtons;
